# App Configuration
NEXT_PUBLIC_APP_NAME=Singapore Legal Help
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_ENVIRONMENT=development

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-singapore-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Payment Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# NETS Configuration
NETS_MERCHANT_ID=your-nets-merchant-id
NETS_SECRET_KEY=your-nets-secret-key

# aiXplain Configuration
AIXPLAIN_API_KEY=your-aixplain-api-key
AIXPLAIN_TEAM_ID=your-team-id

# OpenAI Configuration (for RAG system)
OPENAI_API_KEY=your-openai-api-key

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# Analytics and Monitoring
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Error Tracking and Alerts
SLACK_ERROR_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
EMAIL_SERVICE_URL=https://api.emailservice.com/send
ADMIN_EMAIL=<EMAIL>

# Security Monitoring
SECURITY_WEBHOOK_URL=https://hooks.slack.com/services/your/security/webhook
SECURITY_WEBHOOK_TOKEN=your-security-webhook-token
SECURITY_ALERT_WEBHOOK_URL=https://hooks.slack.com/services/your/security/alerts
