'use client'

import React, { useState, useEffect } from 'react'
import { 
  Eye, 
  Download, 
  Save, 
  Share2, 
  FileText, 
  Printer,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize2,
  X
} from 'lucide-react'

interface DocumentPreviewProps {
  templateId: string
  templateTitle: string
  variables: Record<string, any>
  outputFormat: 'docx' | 'pdf'
  onClose: () => void
  onDownload: () => void
  onSave?: () => void
  isGenerating?: boolean
}

export default function EnhancedDocumentPreview({
  templateId,
  templateTitle,
  variables,
  outputFormat,
  onClose,
  onDownload,
  onSave,
  isGenerating = false
}: DocumentPreviewProps) {
  const [previewContent, setPreviewContent] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [zoom, setZoom] = useState(100)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    generatePreview()
  }, [templateId, variables, outputFormat])

  const generatePreview = async () => {
    try {
      setLoading(true)
      setError(null)

      // Generate preview content based on variables
      const preview = generatePreviewContent(templateTitle, variables)
      setPreviewContent(preview)

    } catch (error) {
      console.error('Preview generation error:', error)
      setError('Failed to generate preview')
    } finally {
      setLoading(false)
    }
  }

  const generatePreviewContent = (title: string, vars: Record<string, any>): string => {
    // Create a formatted preview of the document
    let content = `
      <div class="document-preview">
        <div class="document-header">
          <h1 class="document-title">${title}</h1>
          <div class="document-meta">
            <p>Generated on: ${new Date().toLocaleDateString('en-SG')}</p>
            <p>Format: ${outputFormat.toUpperCase()}</p>
          </div>
        </div>
        
        <div class="document-body">
    `

    // Add variable content
    Object.entries(vars).forEach(([key, value]) => {
      if (value && typeof value === 'string' && value.trim()) {
        const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        content += `
          <div class="field-group">
            <label class="field-label">${label}:</label>
            <span class="field-value">${value}</span>
          </div>
        `
      }
    })

    content += `
        </div>
        
        <div class="document-footer">
          <p class="legal-notice">
            This document was generated by Singapore Legal Help. 
            Please review carefully and consult a lawyer if needed.
          </p>
          <p class="compliance-notice">
            ✓ Singapore law compliant • ✓ PDPA compliant
          </p>
        </div>
      </div>
    `

    return content
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50))
  }

  const handlePrint = () => {
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${templateTitle}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .document-title { color: #1f2937; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
              .document-meta { color: #6b7280; font-size: 14px; margin: 10px 0; }
              .field-group { margin: 15px 0; }
              .field-label { font-weight: bold; color: #374151; }
              .field-value { margin-left: 10px; }
              .legal-notice { font-size: 12px; color: #6b7280; margin-top: 30px; }
              .compliance-notice { font-size: 12px; color: #059669; }
            </style>
          </head>
          <body>${previewContent}</body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: templateTitle,
          text: `Document preview: ${templateTitle}`,
          url: window.location.href
        })
      } catch (error) {
        console.log('Share cancelled or failed')
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Generating Preview</h3>
            <p className="text-gray-600">Please wait while we prepare your document preview...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <FileText className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Preview Error</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex space-x-3">
              <button
                onClick={generatePreview}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Retry
              </button>
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${isFullscreen ? 'p-0' : 'p-4'}`}>
      <div className={`bg-white rounded-lg flex flex-col ${isFullscreen ? 'w-full h-full' : 'max-w-4xl w-full h-5/6'}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Document Preview</h2>
            <p className="text-sm text-gray-600">{templateTitle}</p>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Zoom Controls */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
              <button
                onClick={handleZoomOut}
                className="p-1 hover:bg-gray-200 rounded"
                disabled={zoom <= 50}
              >
                <ZoomOut className="h-4 w-4" />
              </button>
              <span className="text-sm font-medium px-2">{zoom}%</span>
              <button
                onClick={handleZoomIn}
                className="p-1 hover:bg-gray-200 rounded"
                disabled={zoom >= 200}
              >
                <ZoomIn className="h-4 w-4" />
              </button>
            </div>

            {/* Action Buttons */}
            <button
              onClick={handlePrint}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Print"
            >
              <Printer className="h-5 w-5" />
            </button>
            
            <button
              onClick={handleShare}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Share"
            >
              <Share2 className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Fullscreen"
            >
              <Maximize2 className="h-5 w-5" />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Close"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-auto bg-gray-100 p-4">
          <div 
            className="bg-white shadow-lg mx-auto p-8 min-h-full"
            style={{ 
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top center',
              width: `${10000 / zoom}%`,
              maxWidth: '210mm', // A4 width
              minHeight: '297mm' // A4 height
            }}
          >
            <div 
              dangerouslySetInnerHTML={{ __html: previewContent }}
              className="preview-content"
            />
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Preview generated • {Object.keys(variables).length} fields populated
          </div>
          
          <div className="flex space-x-3">
            {onSave && (
              <button
                onClick={onSave}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </button>
            )}
            
            <button
              onClick={onDownload}
              disabled={isGenerating}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Generating...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Download {outputFormat.toUpperCase()}
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .preview-content .document-title {
          color: #1f2937;
          font-size: 24px;
          font-weight: bold;
          border-bottom: 2px solid #3b82f6;
          padding-bottom: 10px;
          margin-bottom: 20px;
        }
        
        .preview-content .document-meta {
          color: #6b7280;
          font-size: 14px;
          margin-bottom: 30px;
        }
        
        .preview-content .field-group {
          margin: 15px 0;
          display: flex;
          align-items: baseline;
        }
        
        .preview-content .field-label {
          font-weight: bold;
          color: #374151;
          min-width: 150px;
          margin-right: 10px;
        }
        
        .preview-content .field-value {
          color: #1f2937;
        }
        
        .preview-content .document-footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
        }
        
        .preview-content .legal-notice {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 10px;
        }
        
        .preview-content .compliance-notice {
          font-size: 12px;
          color: #059669;
          font-weight: 500;
        }
      `}</style>
    </div>
  )
}
