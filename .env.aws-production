# AWS Amplify Production Environment Configuration
# Singapore Legal Help Platform - singaporelegalhelp.com.sg
# 
# ⚠️ IMPORTANT: Only use these values AFTER custom domain is fully configured
# Current working domain: https://main.d2s0gf51rcbiy5.amplifyapp.com/

# App Configuration - UPDATE ONLY AFTER DOMAIN IS ACTIVE
NEXT_PUBLIC_APP_NAME=Singapore Legal Help
NEXT_PUBLIC_APP_URL=https://www.singaporelegalhelp.com.sg
NEXT_PUBLIC_ENVIRONMENT=production

# AWS Amplify Specific
AWS_REGION=ap-southeast-1
AWS_APP_ID=d2s0gf51rcbiy5

# Supabase Production Configuration
# ✅ These should already be working - DO NOT CHANGE unless needed
NEXT_PUBLIC_SUPABASE_URL=https://ooqhzdavkjlyjxqrhkwt.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-current-working-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-current-working-service-role-key

# Payment Configuration - LIVE KEYS
# ⚠️ Only update to live keys when ready for production payments
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-live-key
STRIPE_SECRET_KEY=sk_live_your-stripe-live-secret
STRIPE_WEBHOOK_SECRET=whsec_your-live-webhook-secret

# NETS Production Configuration
NETS_MERCHANT_ID=your-production-nets-merchant-id
NETS_SECRET_KEY=your-production-nets-secret-key
NETS_ENVIRONMENT=production

# aiXplain Production Configuration
AIXPLAIN_API_KEY=your-production-aixplain-api-key
AIXPLAIN_TEAM_ID=your-production-team-id
AIXPLAIN_ENVIRONMENT=production

# Security Configuration
# ⚠️ Generate a new strong secret for production
NEXTAUTH_SECRET=your-super-secure-production-secret-min-32-characters-long
NEXTAUTH_URL=https://www.singaporelegalhelp.com.sg

# Analytics and Monitoring
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production

# Error Tracking and Alerts (Production)
SLACK_ERROR_WEBHOOK_URL=https://hooks.slack.com/services/your/production/error/webhook
EMAIL_SERVICE_URL=https://api.emailservice.com/send
ADMIN_EMAIL=<EMAIL>

# Security Monitoring (Production)
SECURITY_WEBHOOK_URL=https://hooks.slack.com/services/your/production/security/webhook
SECURITY_WEBHOOK_TOKEN=your-production-security-webhook-token
SECURITY_ALERT_WEBHOOK_URL=https://hooks.slack.com/services/your/production/security/alerts

# AWS CloudWatch (Optional)
CLOUDWATCH_LOG_GROUP=/aws/amplify/singapore-legal-help
CLOUDWATCH_REGION=ap-southeast-1

# Performance and Caching
# AWS CloudFront is automatically configured by Amplify
CDN_URL=https://www.singaporelegalhelp.com.sg

# Legal and Compliance
PDPA_COMPLIANCE_MODE=strict
DATA_RETENTION_DAYS=2555  # 7 years for legal documents
AUDIT_LOGGING=enabled

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password
FROM_EMAIL=Singapore Legal Help <<EMAIL>>

# Backup and Recovery
BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=90

# Feature Flags
FEATURE_DOCUMENT_BUILDER=true
FEATURE_AI_CHAT=true
FEATURE_PAYMENT_PROCESSING=true
FEATURE_PWA=true
FEATURE_ANALYTICS=true

# AWS Amplify Build Configuration
# These are automatically set by AWS Amplify
# AWS_BRANCH=main
# AWS_COMMIT_ID=auto-generated
# AWS_APP_ID=d2s0gf51rcbiy5
# AWS_REGION=ap-southeast-1
