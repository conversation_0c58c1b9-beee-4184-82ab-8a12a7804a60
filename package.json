{"name": "singapore-legal-help", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:rag": "jest src/tests/rag-system.test.ts", "validate:rag": "node scripts/validate-rag-system.js", "test:rag:comprehensive": "node scripts/comprehensive-rag-test.js", "test:integration": "npm run validate:rag", "test:all": "npm run test && npm run validate:rag && npm run test:rag:comprehensive", "test:performance": "node scripts/performance-test.js", "test:performance:prod": "TEST_URL=https://singaporelegalhelp.com.sg node scripts/performance-test.js", "analyze": "cross-env ANALYZE=true next build", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./performance-reports/lighthouse-report.html"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@stripe/stripe-js": "^2.4.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.50.3", "@tanstack/react-query": "^5.8.4", "@types/pdfkit": "^0.14.0", "autoprefixer": "^10.0.1", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "docxtemplater": "^3.65.1", "dotenv": "^17.0.1", "jspdf": "^3.0.1", "lucide-react": "^0.294.0", "next": "^14.2.30", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "openai": "^5.8.3", "pdfkit": "^0.17.1", "pizzip": "^3.2.0", "postcss": "^8", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "recharts": "^3.0.2", "stripe": "^14.25.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.0", "typescript": "^5", "web-vitals": "^3.5.0", "workbox-webpack-plugin": "^7.3.0", "zod": "^3.25.75", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.30", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lighthouse": "^11.4.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "puppeteer": "^21.6.1", "ts-jest": "^29.4.0"}}